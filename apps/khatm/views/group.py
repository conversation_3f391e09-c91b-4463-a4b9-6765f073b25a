import hashlib
import random
import string
from django.db.models import Subquery, OuterRef, Sum, Q, F
from django_filters import rest_framework as filters
from rest_framework import generics
from django.utils.translation import gettext as _
from rest_framework.generics import get_object_or_404
from rest_framework.permissions import IsAuthenticated
from rest_framework import generics, permissions
from rest_framework.exceptions import PermissionDenied
from django.db.models import Count, F, Q
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from django.shortcuts import get_object_or_404
from rest_framework.response import Response
from rest_framework import status
from django.db.models import Max
from rest_framework.exceptions import ValidationError
from dj_language.models import Language

from apps.khatm.models import IndividualKhatm, GroupKhatm, UserKhatm
from apps.khatm.serializers import GroupKhatmSerializer, JoinKhatmSerializer, ReadPageSerializer, \
    UserGroupKhatmSerializer, JoinKhatmSerializer
from utils.pageless import PageLessMixin
from apps.khatm.filters import GroupKhatmFilter
from apps.dua.pagination import NoPagination






# permission owner only delete or update 
class IsOwnerOrReadOnly(permissions.BasePermission):
    def has_object_permission(self, request, view, obj):
        # Read permissions are allowed to any request
        # so we'll always allow GET, HEAD, or OPTIONS requests.
        if request.method in permissions.SAFE_METHODS:
            return True

        # Write permissions are only allowed to the owner of the khatm
        return obj.created_by == request.user




class JoinedGroupsView(PageLessMixin, generics.ListAPIView):
    serializer_class = UserGroupKhatmSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return UserKhatm.objects.filter(
            user=self.request.user,
        ).select_related('khatm__language', 'khatm__created_by', 'khatm')




class GroupKhatmListCreateAPIView(generics.ListCreateAPIView):
    queryset = GroupKhatm.objects.all()
    pagination_class = NoPagination
    serializer_class = GroupKhatmSerializer

    def get_permissions(self):
        """
        Allow anonymous users to list groups, but require authentication for creating groups
        """
        if self.request.method == 'POST':
            return [IsAuthenticated()]
        return []
    
    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                'page',
                openapi.IN_QUERY,
                description="Filter to show only the groups page",
                type=openapi.TYPE_STRING,
                enum=['created', 'joined']
            ),
        ]
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)
    
    def get_queryset(self):
        user = self.request.user
        language_code = self.request.LANGUAGE_CODE
        queryset = self.queryset.all()
        queryset.annotate(
            users_count=Count('users'),
            # progress_percentage=F('total_pages_read') * 100 / 604
        )

        if user.is_authenticated:
            queryset = queryset.filter(
                Q(group_type=GroupKhatm.Type.public) |
                Q(group_type=GroupKhatm.Type.private, created_by=user) |
                Q(users__user=user)
            ).distinct()
        else:
            queryset = queryset.filter(
                Q(group_type=GroupKhatm.Type.public)
            ).distinct()
            
        if page_filter := self.request.query_params.get('page'):
            if page_filter == "created" and user.is_authenticated:
                queryset =  queryset.filter(created_by=self.request.user)
            elif page_filter == "joined" and user.is_authenticated:
                queryset = queryset.filter(Q(users__user=user)).exclude(created_by=user)
        else:
            if user.is_authenticated:
                queryset = queryset.exclude(created_by=user)
                            
        queryset = queryset.filter(
            Q(group_type=GroupKhatm.Type.private) |  
            (
                Q(group_type=GroupKhatm.Type.public) &
                Q(translations__contains=[{"language_code": language_code}])
            )
        )
        # queryset = queryset.filter(
        #     Q(translations__contains=[{"language_code": language_code}]) 
        #     # ~Q(translations__contains=[{"language_code": language_code, "text": ""}]) |
        #     # Q(translations={}, language__code=language_code) |
        #     # Q(language__code=language_code)
        # )            
        return queryset.order_by('-pin_to_top', '-created_at')
            
    def perform_create(self, serializer):
        language = Language.objects.get(code=self.request.LANGUAGE_CODE)
        name = serializer.validated_data.get('name')
        serializer.save(
            created_by=self.request.user,
            language=language,
            translations=[{"text": name, "language_code": language.code}]
        )







class GroupReadPageView(generics.UpdateAPIView):
    permission_classes = [IsAuthenticated]
    serializer_class = ReadPageSerializer
    http_method_names = ['put']
    lookup_field = 'slug'
    lookup_url_kwarg = 'slug'

    def get_queryset(self):
        user = self.request.user
        slug = self.kwargs['slug']
        return UserKhatm.objects.filter(user=user, khatm__slug=slug)    

    def update(self, request, *args, **kwargs):
        khatms = self.get_queryset()
        page_user_id = request.data.get('page_user_id')

        try:
            # پیدا کردن شیء خاص در queryset بر اساس page_user_id
            user_khatm = khatms.get(id=page_user_id)
        except UserKhatm.DoesNotExist:
            return Response({"detail": "Not found."}, status=status.HTTP_404_NOT_FOUND)
        serializer = self.get_serializer(user_khatm, data=request.data, partial=True)

        if serializer.is_valid():
            user_khatm.last_page_read = user_khatm.pages_count
            user_khatm.save()

            response_data = serializer.data
            response_data.update({
                'message': 'Page read successfully recorded.',
                'last_page_read': user_khatm.last_page_read,
                'total_pages_read_group': user_khatm.khatm.total_pages_read,
                'remaining_pages': user_khatm.pages_count - user_khatm.last_page_read
            })
            return Response(response_data, status=status.HTTP_200_OK)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)



class GroupKhatmDetailView(generics.RetrieveUpdateDestroyAPIView):
    queryset = GroupKhatm.objects.all()
    serializer_class = GroupKhatmSerializer
    permission_classes = [IsOwnerOrReadOnly]
    lookup_field = 'slug'

    # todo: check if current user is the owner of group to perform update, delete

    def perform_destroy(self, instance):
        if instance.created_by != self.request.user:
            raise PermissionDenied("You do not have permission to delete this group.")
        instance.delete()
        
    def perform_update(self, serializer):
        if self.get_object().created_by != self.request.user:
            raise PermissionDenied("You do not have permission to update this group.")
        serializer.save()



class JoinGroupKhatmAPIView(generics.CreateAPIView):
    serializer_class = JoinKhatmSerializer

    @swagger_auto_schema(
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'pages_count': openapi.Schema(type=openapi.TYPE_INTEGER, description='Number of pages to read'),
            },
            required=['pages_count'],
        )
    )
    def post(self, request, *args, **kwargs):
        return super().post(request, *args, **kwargs)
    
    def get_serializer_context(self):
        context = super().get_serializer_context()
        context['slug'] = self.kwargs['slug']
        return context


